# Augment Agent 用户指南 v4.0

## 🤖 关于 Augment Agent

Augment Agent 是由 Augment Code 开发的智能编程助手，基于 Anthropic 的 Claude Sonnet 4 模型，通过世界领先的上下文引擎和集成功能，为开发者提供强大的代码库访问和编程支持。

## 📋 核心功能

### 🔍 双引擎架构
- **ACE (Augment Context Engine)**: 深度理解项目代码库
- **Context7**: 查询最新外部文档和库信息
- **智能协同**: 双引擎配合提供最准确的编程支持

### 🛠️ 主要能力
- **代码分析**: 深度理解现有代码结构和逻辑
- **智能编写**: 基于最新文档编写高质量代码
- **实时调研**: 自动获取第三方库的最新版本文档
- **任务管理**: 复杂项目的结构化规划和进度跟踪
- **测试支持**: 编写和运行单元测试确保代码质量

### 🎭 **新增: Playwright 集成**
- **端到端测试**: 自动化Web应用测试
- **浏览器自动化**: 跨浏览器兼容性测试
- **截图和录制**: 自动生成测试报告和视觉回归测试
- **性能监控**: 页面加载时间和性能指标分析

### 🧠 **新增: Sequential Thinking**
- **结构化推理**: 复杂问题的逐步分解和分析
- **思维链可视化**: 清晰展示解决问题的逻辑过程
- **决策树生成**: 多方案对比和最优选择
- **调试辅助**: 系统性排查和解决技术问题

## 🚀 使用流程

### 标准工作流程
1. **获取系统时间** → 确保时间戳准确性
2. **ACE项目分析** → 深度理解当前代码库
3. **Context7文档调研** → 获取相关组件最新文档
4. **Sequential Thinking分析** → 复杂问题的结构化分解
5. **需求澄清** → 确认具体要求和期望
6. **基于调研编写代码** → 使用最新信息编写代码
7. **Playwright测试验证** → 自动化测试确保功能正确
8. **收集反馈** → 每步都获取用户反馈优化

### 增强工作流程 (复杂项目)
```mermaid
graph LR
    A[时间获取] --> B[ACE分析]
    B --> C[Context7调研]
    C --> D[Sequential思考]
    D --> E[需求澄清]
    E --> F[代码实现]
    F --> G[Playwright测试]
    G --> H[反馈收集]
    H --> I{是否完成?}
    I -->|否| D
    I -->|是| J[项目完成]
```

### 时间戳格式
每次回复都会显示准确的模型标签：
```
[MODEL: Claude Sonnet 4 | STEP: 当前步骤 | TIME: YYYY-MM-DD HH:MM:SS +08:00]
```

## 💡 最佳使用方式

### 1. 明确需求描述
```
✅ 好的描述：
"我需要在React项目中添加一个用户认证功能，使用JWT token，包括登录、注册和权限验证"

❌ 模糊描述：
"帮我做个登录功能"
```

### 2. 提供上下文信息
- 说明项目类型和技术栈
- 指出相关的现有代码文件
- 描述预期的功能和性能要求

### 3. 分步骤处理复杂任务
- 大型功能会自动分解为子任务
- 每个步骤完成后会请求反馈
- 支持任务优先级调整和重新规划

## 🔧 高级功能

### 任务管理系统
```
[ ] 未开始任务
[/] 进行中任务  
[x] 已完成任务
[-] 已取消任务
```

### 代码展示格式
代码会以特殊格式展示，支持点击查看完整文件：
```xml
<augment_code_snippet path="src/components/Login.jsx" mode="EXCERPT">
````javascript
const LoginComponent = () => {
  // 代码内容...
}
````
</augment_code_snippet>
```

### 包管理器支持
- 自动使用正确的包管理器 (npm, yarn, pip, cargo等)
- 避免手动编辑配置文件
- 自动解决依赖冲突

## 🎯 专业建议

### 编程最佳实践
1. **测试驱动**: 建议为新代码编写测试
2. **渐进式开发**: 先实现核心功能，再添加特性
3. **代码审查**: 会主动指出潜在问题和改进建议
4. **性能优化**: 提供性能优化建议和最佳实践

### 现代框架推荐
- **Web应用**: React + Vite 或 Next.js
- **数据库**: Supabase (包含认证功能)
- **部署**: Vercel, Netlify, Cloudflare
- **测试**: Jest, Vitest, Cypress

## 🛡️ 安全和限制

### 安全原则
- 不会执行危险操作 (如删除重要文件)
- 需要明确授权才能：
  - 提交或推送代码
  - 安装依赖包
  - 部署应用
  - 修改系统配置

### 使用限制
- 专注于编程相关任务
- 不提供与编程无关的服务
- 遵循用户明确指示，不擅自扩展功能

## 🔄 反馈机制

### 持续反馈
- 每个步骤完成后都会请求反馈
- 支持实时调整和优化
- 只有用户明确说"结束"才停止交互

### 反馈类型
- **功能反馈**: 对实现的功能是否满意
- **性能反馈**: 代码性能和优化建议
- **需求调整**: 修改或添加新的需求
- **问题报告**: 发现的bug或问题

## 📚 学习和改进

### 记忆系统
- 会记住用户的偏好和习惯
- 学习项目特定的编码风格
- 积累常用的解决方案模式

### 持续优化
- 根据用户反馈改进回答质量
- 更新最新的技术栈和最佳实践
- 优化代码生成的准确性和效率

## 🤝 协作建议

### 有效沟通
1. **具体描述问题**: 提供错误信息、期望结果
2. **分享相关代码**: 指出需要修改的具体文件
3. **说明约束条件**: 技术限制、时间要求等
4. **及时反馈**: 对提供的解决方案给出反馈

### 项目管理
- 支持敏捷开发流程
- 可以创建和跟踪开发任务
- 提供进度报告和里程碑管理

## 🔍 故障排除

### 常见问题
1. **代码不工作**: 会协助调试并提供修复方案
2. **性能问题**: 分析瓶颈并提供优化建议
3. **依赖冲突**: 帮助解决包版本冲突
4. **部署问题**: 协助解决部署和配置问题

### 获取帮助
- 详细描述遇到的问题
- 提供错误日志和堆栈跟踪
- 说明已经尝试的解决方法
- 指出期望的最终结果

## 📈 效率提升技巧

### 快速开发
1. **使用模板**: 利用现代框架的CLI工具
2. **代码复用**: 识别和提取可复用组件
3. **自动化测试**: 编写测试确保代码质量
4. **持续集成**: 设置CI/CD流程

### 代码质量
- 遵循行业标准和最佳实践
- 提供代码审查和改进建议
- 确保代码可读性和可维护性
- 优化性能和资源使用

---

**版本**: v3.0  
**基于**: Claude Sonnet 4  
**开发商**: Augment Code  
**更新**: 2025-06-28

**记住**: Augment Agent 是您的编程伙伴，旨在提高开发效率和代码质量。充分利用双引擎架构和反馈机制，让每次协作都更加高效！
